package com.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.entity.Dish;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 菜品Mapper接口
 * 
 * <AUTHOR> System
 * @since 2023-07-15
 */
@Mapper
public interface DishMapper extends BaseMapper<Dish> {

    /**
     * 分页查询菜品列表
     */
    @Select("<script>" +
            "SELECT * FROM dish WHERE deleted = 0" +
            "<if test='name != null and name != \"\"'>" +
            " AND name LIKE CONCAT('%', #{name}, '%')" +
            "</if>" +
            "<if test='category != null and category != \"\"'>" +
            " AND category = #{category}" +
            "</if>" +
            "<if test='status != null'>" +
            " AND status = #{status}" +
            "</if>" +
            " ORDER BY created_at DESC" +
            "</script>")
    IPage<Dish> selectDishPage(Page<Dish> page,
                               @Param("name") String name,
                               @Param("category") String category,
                               @Param("status") Integer status);

    /**
     * 获取可用菜品列表
     */
    @Select("SELECT * FROM dish WHERE status = 1 AND deleted = 0 ORDER BY category, created_at DESC")
    List<Dish> selectAvailableDishes();

    /**
     * 根据分类获取菜品
     */
    @Select("SELECT * FROM dish WHERE category = #{category} AND status = 1 AND deleted = 0 ORDER BY created_at DESC")
    List<Dish> selectDishesByCategory(@Param("category") String category);

    /**
     * 获取菜品分类统计
     */
    @Select("SELECT category, COUNT(*) as count FROM dish WHERE deleted = 0 GROUP BY category")
    List<Map<String, Object>> selectCategoryStats();

    /**
     * 获取热销菜品统计
     */
    @Select("SELECT d.id, d.name, d.category, d.price, d.image_url, " +
            "COALESCE(SUM(oi.quantity), 0) as sold_count, " +
            "COALESCE(SUM(oi.quantity * oi.price), 0) as revenue " +
            "FROM dish d " +
            "LEFT JOIN order_item oi ON d.id = oi.dish_id " +
            "LEFT JOIN orders o ON oi.order_id = o.id " +
            "WHERE d.deleted = 0 AND (o.deleted = 0 OR o.id IS NULL) " +
            "GROUP BY d.id, d.name, d.category, d.price, d.image_url " +
            "ORDER BY sold_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectHotDishes(@Param("limit") Integer limit);

    /**
     * 检查菜品名称是否存在
     */
    @Select("SELECT COUNT(*) FROM dish WHERE name = #{name} AND deleted = 0 AND id != #{excludeId}")
    int checkDishNameExists(@Param("name") String name, @Param("excludeId") Integer excludeId);

    /**
     * 统计菜品总数
     */
    @Select("SELECT COUNT(*) FROM dish WHERE deleted = 0")
    Integer countTotalDishes();
}
