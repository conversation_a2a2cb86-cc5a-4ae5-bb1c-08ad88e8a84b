<template>
  <Layout>
    <div class="notifications-container">
      <div class="page-header">
        <h2>
          <el-icon><Bell /></el-icon>
          通知中心
        </h2>
        <div class="header-actions">
          <el-button 
            type="primary" 
            @click="markAllAsRead"
            :disabled="unreadCount === 0"
          >
            <el-icon><Check /></el-icon>
            全部标记为已读
          </el-button>
          <el-button @click="refreshNotifications">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>

      <!-- 统计信息 -->
      <div class="stats-bar">
        <el-card shadow="never" class="stats-card">
          <div class="stats-content">
            <div class="stat-item">
              <span class="stat-label">总通知</span>
              <span class="stat-value">{{ pagination.total }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">未读</span>
              <span class="stat-value unread">{{ unreadCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已读</span>
              <span class="stat-value">{{ pagination.total - unreadCount }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 筛选器 -->
      <div class="filter-bar">
        <el-radio-group v-model="filterType" @change="loadNotifications">
          <el-radio-button label="all">全部</el-radio-button>
          <el-radio-button label="unread">未读</el-radio-button>
          <el-radio-button label="read">已读</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 通知列表 -->
      <div class="notifications-list" v-loading="loading">
        <el-empty v-if="notifications.length === 0 && !loading" description="暂无通知" />
        
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="notification-item"
          :class="{ 'unread': !notification.isRead }"
          @click="handleNotificationClick(notification)"
        >
          <div class="notification-icon">
            <el-icon v-if="notification.type === 'order'" class="order-icon">
              <Document />
            </el-icon>
            <el-icon v-else-if="notification.type === 'system'" class="system-icon">
              <Setting />
            </el-icon>
            <el-icon v-else class="info-icon">
              <InfoFilled />
            </el-icon>
          </div>
          
          <div class="notification-content">
            <div class="notification-header">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <div class="notification-meta">
                <el-tag 
                  :type="getNotificationTypeColor(notification.type)" 
                  size="small"
                >
                  {{ getNotificationTypeText(notification.type) }}
                </el-tag>
                <span class="notification-time">
                  {{ formatTime(notification.createdAt) }}
                </span>
              </div>
            </div>
            <p class="notification-message">{{ notification.message }}</p>
          </div>
          
          <div class="notification-actions">
            <el-button 
              v-if="!notification.isRead"
              type="text" 
              size="small"
              @click.stop="markAsRead(notification.id)"
            >
              标记已读
            </el-button>
            <el-button 
              type="text" 
              size="small"
              @click.stop="deleteNotification(notification.id)"
            >
              删除
            </el-button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="pagination.total > 0">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadNotifications"
          @current-change="loadNotifications"
        />
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Bell, Check, Refresh, Document, Setting, InfoFilled
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'

const router = useRouter()
const loading = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const filterType = ref('all')

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 加载通知列表
const loadNotifications = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size
    }
    
    const response = await api.get('/api/notifications', { params })
    const { records, total } = response.data.data
    
    notifications.value = records || []
    pagination.total = total || 0
    
    // 计算未读数量
    unreadCount.value = notifications.value.filter(n => !n.isRead).length
  } catch (error) {
    console.error('加载通知失败:', error)
    // 使用模拟数据
    notifications.value = getMockNotifications()
    pagination.total = notifications.value.length
    unreadCount.value = notifications.value.filter(n => !n.isRead).length
    ElMessage.warning('使用模拟通知数据')
  } finally {
    loading.value = false
  }
}

// 获取模拟通知数据
const getMockNotifications = () => {
  return [
    {
      id: 1,
      title: '新订单提醒',
      message: '您有一个新的订单需要处理，订单号：#2024001',
      type: 'order',
      isRead: false,
      createdAt: new Date().toISOString(),
      link: '/orders/1'
    },
    {
      id: 2,
      title: '系统维护通知',
      message: '系统将于今晚23:00-24:00进行维护，请提前做好准备',
      type: 'system',
      isRead: true,
      createdAt: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 3,
      title: '菜品库存不足',
      message: '宫保鸡丁库存不足，请及时补充',
      type: 'warning',
      isRead: false,
      createdAt: new Date(Date.now() - 7200000).toISOString()
    }
  ]
}

// 标记单个通知为已读
const markAsRead = async (id) => {
  try {
    await api.put(`/api/notifications/${id}/read`)
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.isRead = true
      unreadCount.value--
    }
    ElMessage.success('已标记为已读')
  } catch (error) {
    console.error('标记已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 标记所有通知为已读
const markAllAsRead = async () => {
  try {
    await api.put('/api/notifications/read-all')
    notifications.value.forEach(n => n.isRead = true)
    unreadCount.value = 0
    ElMessage.success('所有通知已标记为已读')
  } catch (error) {
    console.error('标记所有已读失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除通知
const deleteNotification = async (id) => {
  try {
    await ElMessageBox.confirm('确定要删除这条通知吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await api.delete(`/api/notifications/${id}`)
    notifications.value = notifications.value.filter(n => n.id !== id)
    pagination.total--
    ElMessage.success('通知已删除')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除通知失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  if (!notification.isRead) {
    markAsRead(notification.id)
  }
  
  if (notification.link) {
    router.push(notification.link)
  }
}

// 刷新通知
const refreshNotifications = () => {
  loadNotifications()
  ElMessage.success('通知已刷新')
}

// 获取通知类型颜色
const getNotificationTypeColor = (type) => {
  const colorMap = {
    'order': 'primary',
    'system': 'info',
    'warning': 'warning',
    'error': 'danger'
  }
  return colorMap[type] || 'info'
}

// 获取通知类型文本
const getNotificationTypeText = (type) => {
  const textMap = {
    'order': '订单',
    'system': '系统',
    'warning': '警告',
    'error': '错误'
  }
  return textMap[type] || '通知'
}

// 格式化时间
const formatTime = (timeStr) => {
  const time = new Date(timeStr)
  const now = new Date()
  const diff = now - time
  
  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return time.toLocaleDateString()
  }
}

onMounted(() => {
  loadNotifications()
})
</script>

<style scoped>
.notifications-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: #3498db;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-bar {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
}

.stats-content {
  display: flex;
  gap: 40px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 0.9rem;
  color: #6c757d;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #3498db;
}

.stat-value.unread {
  color: #e74c3c;
}

.filter-bar {
  margin-bottom: 20px;
}

.notifications-list {
  min-height: 400px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  margin-bottom: 15px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s;
}

.notification-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.notification-item.unread {
  border-left: 4px solid #3498db;
  background: #f8f9ff;
}

.notification-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.order-icon {
  background: #e3f2fd;
  color: #1976d2;
}

.system-icon {
  background: #f3e5f5;
  color: #7b1fa2;
}

.info-icon {
  background: #e8f5e8;
  color: #388e3c;
}

.notification-content {
  flex: 1;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notification-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #343a40;
}

.notification-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.notification-time {
  font-size: 0.8rem;
  color: #6c757d;
}

.notification-message {
  margin: 0;
  color: #6c757d;
  line-height: 1.5;
}

.notification-actions {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .stats-content {
    justify-content: space-around;
  }

  .notification-item {
    flex-direction: column;
    gap: 10px;
  }

  .notification-header {
    flex-direction: column;
    gap: 10px;
  }

  .notification-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
}
</style>
