package com.hotel.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hotel.common.PageResult;
import com.hotel.entity.Dish;
import com.hotel.entity.Order;
import com.hotel.entity.User;
import com.hotel.service.DishService;
import com.hotel.service.ExportService;
import com.hotel.service.OrderService;
import com.hotel.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 导出服务实现类
 */
@Slf4j
@Service
public class ExportServiceImpl implements ExportService {
    
    @Autowired
    private OrderService orderService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private DishService dishService;
    
    @Override
    public ByteArrayOutputStream exportOrders(String startDate, String endDate) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("订单报表");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"订单ID", "桌号", "用户名", "状态", "总金额", "创建时间", "更新时间"};
            
            CellStyle headerStyle = createHeaderStyle(workbook);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 查询订单数据 - 使用现有的分页查询方法获取所有数据
            LocalDateTime startDateTime = null;
            LocalDateTime endDateTime = null;
            if (StringUtils.hasText(startDate)) {
                startDateTime = LocalDate.parse(startDate).atStartOfDay();
            }
            if (StringUtils.hasText(endDate)) {
                endDateTime = LocalDate.parse(endDate).atTime(23, 59, 59);
            }

            // 使用一个大的页面大小来获取所有数据
            PageResult<Order> pageResult = orderService.getOrderPage(1, 10000, null, null, null, startDateTime, endDateTime);
            List<Order> orders = pageResult.getRecords();
            
            // 填充数据
            CellStyle dataStyle = createDataStyle(workbook);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (int i = 0; i < orders.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Order order = orders.get(i);
                
                createCell(row, 0, order.getId(), dataStyle);
                createCell(row, 1, order.getTableNumber(), dataStyle);
                createCell(row, 2, getUserName(order.getUserId()), dataStyle);
                createCell(row, 3, getOrderStatusText(order.getStatus()), dataStyle);
                createCell(row, 4, order.getTotalPrice(), dataStyle);
                createCell(row, 5, order.getCreatedAt().format(formatter), dataStyle);
                createCell(row, 6, order.getUpdatedAt().format(formatter), dataStyle);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream;
            
        } catch (IOException e) {
            log.error("导出订单报表失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }
    
    @Override
    public ByteArrayOutputStream exportUsers() {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("用户报表");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"用户ID", "用户名", "姓名", "角色", "状态", "最后登录时间", "创建时间"};
            
            CellStyle headerStyle = createHeaderStyle(workbook);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 查询用户数据 - 使用现有的分页查询方法获取所有数据
            PageResult<User> pageResult = userService.getUserPage(1, 10000, null, null, null);
            List<User> users = pageResult.getRecords();
            
            // 填充数据
            CellStyle dataStyle = createDataStyle(workbook);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (int i = 0; i < users.size(); i++) {
                Row row = sheet.createRow(i + 1);
                User user = users.get(i);
                
                createCell(row, 0, user.getId(), dataStyle);
                createCell(row, 1, user.getUsername(), dataStyle);
                createCell(row, 2, user.getName(), dataStyle);
                createCell(row, 3, getUserRoleText(user.getRole()), dataStyle);
                createCell(row, 4, user.getStatus() == 1 ? "启用" : "禁用", dataStyle);
                createCell(row, 5, user.getLastLoginAt() != null ? user.getLastLoginAt().format(formatter) : "", dataStyle);
                createCell(row, 6, user.getCreatedAt().format(formatter), dataStyle);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream;
            
        } catch (IOException e) {
            log.error("导出用户报表失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }
    
    @Override
    public ByteArrayOutputStream exportDishes() {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("菜品报表");
            
            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {"菜品ID", "菜品名称", "分类", "价格", "状态", "创建时间", "更新时间"};
            
            CellStyle headerStyle = createHeaderStyle(workbook);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }
            
            // 查询菜品数据 - 使用现有的分页查询方法获取所有数据
            PageResult<Dish> pageResult = dishService.getDishPage(1, 10000, null, null, null);
            List<Dish> dishes = pageResult.getRecords();
            
            // 填充数据
            CellStyle dataStyle = createDataStyle(workbook);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            for (int i = 0; i < dishes.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Dish dish = dishes.get(i);
                
                createCell(row, 0, dish.getId(), dataStyle);
                createCell(row, 1, dish.getName(), dataStyle);
                createCell(row, 2, getDishCategoryText(dish.getCategory()), dataStyle);
                createCell(row, 3, dish.getPrice(), dataStyle);
                createCell(row, 4, dish.getStatus() == 1 ? "启用" : "禁用", dataStyle);
                createCell(row, 5, dish.getCreatedAt().format(formatter), dataStyle);
                createCell(row, 6, dish.getUpdatedAt().format(formatter), dataStyle);
            }
            
            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream;
            
        } catch (IOException e) {
            log.error("导出菜品报表失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }
    
    @Override
    public ByteArrayOutputStream exportSales(String startDate, String endDate) {
        // 这里可以实现更复杂的销售统计报表
        // 暂时使用订单报表的逻辑
        return exportOrders(startDate, endDate);
    }
    
    // 辅助方法
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }
    
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    private void createCell(Row row, int column, Object value, CellStyle style) {
        Cell cell = row.createCell(column);
        if (value != null) {
            if (value instanceof Number) {
                cell.setCellValue(((Number) value).doubleValue());
            } else {
                cell.setCellValue(value.toString());
            }
        }
        cell.setCellStyle(style);
    }
    
    private String getUserName(Integer userId) {
        if (userId == null) return "";
        try {
            User user = userService.getUserById(userId);
            return user != null ? user.getName() : "";
        } catch (Exception e) {
            return "";
        }
    }
    
    private String getOrderStatusText(String status) {
        switch (status) {
            case "PENDING": return "待处理";
            case "PREPARING": return "制作中";
            case "COMPLETED": return "已完成";
            case "CANCELLED": return "已取消";
            default: return status;
        }
    }
    
    private String getUserRoleText(String role) {
        switch (role) {
            case "ADMIN": return "管理员";
            case "STAFF": return "服务员";
            default: return role;
        }
    }
    
    private String getDishCategoryText(String category) {
        switch (category) {
            case "APPETIZER": return "开胃菜";
            case "MAIN": return "主菜";
            case "DESSERT": return "甜点";
            case "DRINK": return "饮品";
            default: return category;
        }
    }
}
