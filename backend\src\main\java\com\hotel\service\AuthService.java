package com.hotel.service;

import com.hotel.dto.LoginRequest;
import com.hotel.dto.LoginResponse;
import com.hotel.dto.RegisterRequest;
import com.hotel.entity.User;

/**
 * 认证服务接口
 * 
 * <AUTHOR> System
 * @since 2025-07-15
 */
public interface AuthService {

    /**
     * 用户登录
     */
    LoginResponse login(LoginRequest request);

    /**
     * 用户注册
     */
    User register(RegisterRequest request);

    /**
     * 获取当前用户信息
     */
    User getCurrentUser(Integer userId);

    /**
     * 修改密码
     */
    void changePassword(Integer userId, String currentPassword, String newPassword);

    /**
     * 用户登出
     */
    void logout(Integer userId);
}
