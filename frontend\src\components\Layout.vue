<template>
  <el-container class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="header">
      <div class="logo">
        <el-icon class="logo-icon"><House /></el-icon>
        <h1>酒店点单系统</h1>
      </div>
      
      <el-menu
        mode="horizontal"
        :default-active="activeMenu"
        class="nav-menu"
        @select="handleMenuSelect"
      >
        <el-menu-item index="/dashboard">
          <el-icon><Odometer /></el-icon>
          首页
        </el-menu-item>
        <el-menu-item index="/dishes">
          <el-icon><Food /></el-icon>
          菜品管理
        </el-menu-item>
        <el-menu-item index="/orders">
          <el-icon><Document /></el-icon>
          订单管理
        </el-menu-item>
        <el-menu-item v-if="authStore.isAdmin" index="/users">
          <el-icon><User /></el-icon>
          用户管理
        </el-menu-item>
      </el-menu>
      
      <div class="user-controls">
        <!-- 通知图标 -->
        <el-badge :value="unreadCount" :hidden="unreadCount === 0" class="notification-badge">
          <el-button
            type="text"
            @click="showNotifications"
            class="notification-btn"
          >
            <el-icon size="18"><Bell /></el-icon>
          </el-button>
        </el-badge>

        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            {{ authStore.user?.name || '用户' }}
            <el-icon class="el-icon--right"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">个人信息</el-dropdown-item>
              <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <!-- 主体内容 -->
    <el-container>
      <!-- 侧边栏 -->
      <el-aside width="280px" class="sidebar">
        <div class="sidebar-content">
          <h3 class="sidebar-title">
            <el-icon><Menu /></el-icon>
            快捷菜单
          </h3>
          
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            @select="handleMenuSelect"
          >
            <el-menu-item index="/dashboard">
              <el-icon><Odometer /></el-icon>
              控制面板
            </el-menu-item>
            <el-menu-item index="/dishes/add">
              <el-icon><Plus /></el-icon>
              添加菜品
            </el-menu-item>
            <el-menu-item index="/orders/create">
              <el-icon><DocumentAdd /></el-icon>
              创建订单
            </el-menu-item>
            <el-menu-item index="/orders">
              <el-icon><Clock /></el-icon>
              订单历史
            </el-menu-item>
          </el-menu>
          
          <!-- 通知区域 -->
          <div class="notification-area">
            <h3 class="sidebar-title">
              <el-icon><Bell /></el-icon>
              通知
            </h3>
            <div class="notification-item">
              <el-icon class="warning-icon"><Warning /></el-icon>
              您有 {{ pendingOrdersCount }} 个待处理订单
            </div>
            <div class="notification-item">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
              系统运行正常
            </div>
          </div>
        </div>
      </el-aside>
      
      <!-- 主要内容区域 -->
      <el-main class="main-content">
        <slot />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  House, Odometer, Food, Document, User, ArrowDown, Menu, Plus,
  DocumentAdd, Clock, Bell, Warning, InfoFilled
} from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const pendingOrdersCount = ref(0)
const unreadCount = ref(0)

const activeMenu = computed(() => route.path)

const handleMenuSelect = (index) => {
  router.push(index)
}

const showNotifications = () => {
  router.push('/notifications')
}

const loadUnreadCount = async () => {
  try {
    const response = await api.get('/api/notifications/unread-count')
    unreadCount.value = response.data.data
  } catch (error) {
    console.error('加载未读通知数量失败:', error)
  }
}

const handleCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        authStore.logout()
        router.push('/login')
        ElMessage.success('已退出登录')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  // 这里可以加载待处理订单数量等数据
  pendingOrdersCount.value = 5
  loadUnreadCount()
})
</script>

<style scoped>
.layout-container {
  min-height: 100vh;
}

.header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  font-size: 1.5rem;
  color: #3498db;
}

.logo h1 {
  font-size: 1.2rem;
  color: #3498db;
  margin: 0;
}

.nav-menu {
  flex: 1;
  margin: 0 40px;
  border-bottom: none;
}

.user-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.notification-badge {
  margin-right: 5px;
}

.notification-btn {
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.3s;
}

.notification-btn:hover {
  background-color: #f5f5f5;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.sidebar {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-content {
  padding: 20px;
}

.sidebar-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 15px;
  color: #3498db;
  font-size: 1rem;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.sidebar-menu {
  border-right: none;
  margin-bottom: 30px;
}

.notification-area {
  background: #fff8e1;
  padding: 15px;
  border-radius: 8px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.notification-item:last-child {
  margin-bottom: 0;
}

.warning-icon {
  color: #ff9800;
}

.info-icon {
  color: #2196f3;
}

.main-content {
  background: #f8f9fa;
  padding: 20px;
}
</style>
