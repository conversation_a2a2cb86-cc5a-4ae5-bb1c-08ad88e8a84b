package com.hotel.config;

import com.hotel.entity.Dish;
import com.hotel.entity.User;
import com.hotel.mapper.DishMapper;
import com.hotel.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据初始化器
 * 在应用启动时检查并初始化基础数据
 * 
 * <AUTHOR> System
 * @since 2025-07-15
 */
@Slf4j
@Component
public class DataInitializer implements CommandLineRunner {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DishMapper dishMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        try {
            initializeUsers();
            initializeDishes();
            log.info("数据初始化完成");
        } catch (Exception e) {
            log.warn("数据初始化失败，可能是数据库连接问题: {}", e.getMessage());
        }
    }

    /**
     * 初始化用户数据
     */
    private void initializeUsers() {
        try {
            // 检查是否已有用户数据
            Long userCount = userMapper.selectCount(null);
            if (userCount > 0) {
                log.info("用户数据已存在，跳过初始化");
                return;
            }

            List<User> users = new ArrayList<>();

            // 创建管理员用户
            User admin = new User();
            admin.setUsername("admin");
            admin.setPassword(passwordEncoder.encode("123456"));
            admin.setName("系统管理员");
            admin.setRole("ADMIN");
            admin.setStatus(1);
            admin.setCreatedAt(LocalDateTime.now());
            admin.setUpdatedAt(LocalDateTime.now());
            users.add(admin);

            // 创建服务员用户
            User staff1 = new User();
            staff1.setUsername("staff1");
            staff1.setPassword(passwordEncoder.encode("123456"));
            staff1.setName("张小明");
            staff1.setRole("STAFF");
            staff1.setStatus(1);
            staff1.setCreatedAt(LocalDateTime.now());
            staff1.setUpdatedAt(LocalDateTime.now());
            users.add(staff1);

            User staff2 = new User();
            staff2.setUsername("staff2");
            staff2.setPassword(passwordEncoder.encode("123456"));
            staff2.setName("李小红");
            staff2.setRole("STAFF");
            staff2.setStatus(1);
            staff2.setCreatedAt(LocalDateTime.now());
            staff2.setUpdatedAt(LocalDateTime.now());
            users.add(staff2);

            // 批量插入用户
            for (User user : users) {
                userMapper.insert(user);
            }

            log.info("初始化用户数据成功，共创建 {} 个用户", users.size());
        } catch (Exception e) {
            log.error("初始化用户数据失败", e);
        }
    }

    /**
     * 初始化菜品数据
     */
    private void initializeDishes() {
        try {
            // 检查是否已有菜品数据
            Long dishCount = dishMapper.selectCount(null);
            if (dishCount > 0) {
                log.info("菜品数据已存在，跳过初始化");
                return;
            }

            List<Dish> dishes = new ArrayList<>();

            // 开胃菜
            Dish dish1 = new Dish();
            dish1.setName("凉拌黄瓜");
            dish1.setDescription("清爽开胃，夏日必备的凉菜");
            dish1.setPrice(new BigDecimal("18.00"));
            dish1.setCategory("APPETIZER");
            dish1.setStatus(1);
            dish1.setImageUrl("/default-dish.svg");
            dish1.setCreatedAt(LocalDateTime.now());
            dish1.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish1);

            Dish dish8 = new Dish();
            dish8.setName("酸辣土豆丝");
            dish8.setDescription("酸辣开胃，爽脆可口");
            dish8.setPrice(new BigDecimal("15.00"));
            dish8.setCategory("APPETIZER");
            dish8.setStatus(1);
            dish8.setImageUrl("/default-dish.svg");
            dish8.setCreatedAt(LocalDateTime.now());
            dish8.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish8);

            // 主菜
            Dish dish2 = new Dish();
            dish2.setName("宫保鸡丁");
            dish2.setDescription("经典川菜，香辣可口，下饭神器");
            dish2.setPrice(new BigDecimal("38.00"));
            dish2.setCategory("MAIN");
            dish2.setStatus(1);
            dish2.setImageUrl("/default-dish.svg");
            dish2.setCreatedAt(LocalDateTime.now());
            dish2.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish2);

            Dish dish3 = new Dish();
            dish3.setName("蛋炒饭");
            dish3.setDescription("简单美味，老少皆宜的经典主食");
            dish3.setPrice(new BigDecimal("25.00"));
            dish3.setCategory("MAIN");
            dish3.setStatus(1);
            dish3.setImageUrl("/default-dish.svg");
            dish3.setCreatedAt(LocalDateTime.now());
            dish3.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish3);

            Dish dish4 = new Dish();
            dish4.setName("清蒸鲈鱼");
            dish4.setDescription("鲜美嫩滑，营养丰富的健康菜品");
            dish4.setPrice(new BigDecimal("68.10"));
            dish4.setCategory("MAIN");
            dish4.setStatus(1);
            dish4.setImageUrl("/default-dish.svg");
            dish4.setCreatedAt(LocalDateTime.now());
            dish4.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish4);

            Dish dish6 = new Dish();
            dish6.setName("红烧肉");
            dish6.setDescription("肥而不腻，入口即化的经典菜品");
            dish6.setPrice(new BigDecimal("45.00"));
            dish6.setCategory("MAIN");
            dish6.setStatus(1);
            dish6.setImageUrl("/default-dish.svg");
            dish6.setCreatedAt(LocalDateTime.now());
            dish6.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish6);

            // 饮品
            Dish dish5 = new Dish();
            dish5.setName("可乐");
            dish5.setDescription("冰爽可乐，解腻必备饮品");
            dish5.setPrice(new BigDecimal("10.00"));
            dish5.setCategory("DRINK");
            dish5.setStatus(1);
            dish5.setImageUrl("/default-dish.svg");
            dish5.setCreatedAt(LocalDateTime.now());
            dish5.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish5);

            // 甜点
            Dish dish7 = new Dish();
            dish7.setName("提拉米苏");
            dish7.setDescription("意式经典甜品，浓郁香甜");
            dish7.setPrice(new BigDecimal("35.00"));
            dish7.setCategory("DESSERT");
            dish7.setStatus(0); // 暂时下架
            dish7.setImageUrl("/default-dish.svg");
            dish7.setCreatedAt(LocalDateTime.now());
            dish7.setUpdatedAt(LocalDateTime.now());
            dishes.add(dish7);

            // 批量插入菜品
            for (Dish dish : dishes) {
                dishMapper.insert(dish);
            }

            log.info("初始化菜品数据成功，共创建 {} 个菜品", dishes.size());
        } catch (Exception e) {
            log.error("初始化菜品数据失败", e);
        }
    }
}
