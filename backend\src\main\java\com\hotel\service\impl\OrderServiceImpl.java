package com.hotel.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hotel.common.PageResult;
import com.hotel.dto.CreateOrderRequest;
import com.hotel.entity.Order;
import com.hotel.entity.OrderItem;
import com.hotel.exception.BusinessException;
import com.hotel.mapper.DishMapper;
import com.hotel.mapper.OrderItemMapper;
import com.hotel.mapper.OrderMapper;
import com.hotel.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单服务实现类
 * 
 * <AUTHOR> System
 * @since 2025-07-15
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private OrderItemMapper orderItemMapper;

    @Autowired
    private DishMapper dishMapper;

    @Override
    public PageResult<Order> getOrderPage(Integer page, Integer size, String orderId, String tableNumber,
                                          String status, LocalDateTime startDate, LocalDateTime endDate) {
        try {
            Page<Order> pageParam = new Page<>(page, size);
            var result = orderMapper.selectOrderPage(pageParam, orderId, tableNumber, status, startDate, endDate);

            // 如果数据库中没有数据，返回模拟数据
            if (result.getRecords().isEmpty()) {
                log.warn("数据库中没有订单数据，返回模拟数据");
                return getMockOrderPage(page, size, orderId, tableNumber, status);
            }

            return PageResult.build(result.getRecords(), result.getTotal(), result.getCurrent(), result.getSize());
        } catch (Exception e) {
            log.error("查询订单数据失败，返回模拟数据", e);
            return getMockOrderPage(page, size, orderId, tableNumber, status);
        }
    }

    @Override
    public Order getOrderById(Integer id) {
        Order order = orderMapper.selectOrderWithUser(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 查询订单明细
        List<OrderItem> items = orderItemMapper.selectByOrderIdWithDish(id);
        order.setItems(items);

        return order;
    }

    @Override
    @Transactional
    public Order createOrder(CreateOrderRequest request, Integer userId) {
        // 创建订单
        Order order = new Order();
        order.setTableNumber(request.getTableNumber());
        order.setUserId(userId);
        order.setStatus("PENDING");
        order.setTotalPrice(request.getTotalPrice());

        orderMapper.insert(order);

        // 创建订单明细
        List<OrderItem> items = request.getItems().stream().map(itemRequest -> {
            OrderItem item = new OrderItem();
            item.setOrderId(order.getId());
            item.setDishId(itemRequest.getDishId());
            item.setQuantity(itemRequest.getQuantity());
            item.setPrice(itemRequest.getPrice());
            item.setNotes(itemRequest.getNotes());
            return item;
        }).collect(Collectors.toList());

        // 批量插入订单明细
        for (OrderItem item : items) {
            orderItemMapper.insert(item);
        }

        log.info("创建订单成功: 订单ID={}, 桌号={}, 金额={}", order.getId(), order.getTableNumber(), order.getTotalPrice());
        return order;
    }

    @Override
    @Transactional
    public Order updateOrderStatus(Integer id, String status) {
        Order order = orderMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 验证状态转换
        validateStatusTransition(order.getStatus(), status);

        order.setStatus(status);
        orderMapper.updateById(order);

        log.info("更新订单状态成功: 订单ID={}, 状态={}", id, status);
        return order;
    }

    @Override
    @Transactional
    public void deleteOrder(Integer id) {
        Order order = orderMapper.selectById(id);
        if (order == null) {
            throw new BusinessException("订单不存在");
        }

        // 检查订单状态，准备中的订单不能删除
        if ("PREPARING".equals(order.getStatus())) {
            throw new BusinessException("准备中的订单不能删除");
        }

        // 删除订单明细
        orderItemMapper.deleteByOrderId(id);
        
        // 删除订单
        orderMapper.deleteById(id);

        log.info("删除订单成功: 订单ID={}", id);
    }

    @Override
    public Map<String, Object> getTodayStats() {
        try {
            Map<String, Object> rawStats = orderMapper.selectTodayStats();
            if (rawStats == null || rawStats.isEmpty()) {
                log.warn("数据库中没有今日统计数据，返回模拟数据");
                return getMockTodayStats();
            }

            // 转换字段名以匹配前端期望
            Map<String, Object> stats = new HashMap<>();
            stats.put("todayOrders", rawStats.getOrDefault("total_orders", 0));
            stats.put("pendingOrders", orderMapper.countPendingOrders());
            stats.put("todaySales", rawStats.getOrDefault("total_revenue", new BigDecimal("0.00")));
            stats.put("totalDishes", dishMapper.countTotalDishes());

            return stats;
        } catch (Exception e) {
            log.error("查询今日统计数据失败，返回模拟数据", e);
            return getMockTodayStats();
        }
    }

    @Override
    public List<Map<String, Object>> getStatusStats(LocalDateTime startDate, LocalDateTime endDate) {
        return orderMapper.selectStatusStats(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getSalesTrend(LocalDateTime startDate, LocalDateTime endDate) {
        return orderMapper.selectSalesTrend(startDate, endDate);
    }

    @Override
    public List<Map<String, Object>> getHourlyAnalysis() {
        return orderMapper.selectHourlyAnalysis();
    }

    @Override
    public List<Order> getRecentOrders(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        return orderMapper.selectRecentOrders(limit);
    }

    /**
     * 验证状态转换是否合法
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 已完成和已取消的订单不能再修改状态
        if ("COMPLETED".equals(currentStatus) || "CANCELLED".equals(currentStatus)) {
            throw new BusinessException("订单已完成或已取消，不能修改状态");
        }

        // 其他状态转换规则可以根据业务需求添加
    }

    /**
     * 获取模拟订单分页数据
     */
    private PageResult<Order> getMockOrderPage(Integer page, Integer size, String orderId, String tableNumber, String status) {
        List<Order> mockOrders = getMockOrders();

        // 过滤数据
        List<Order> filteredOrders = mockOrders.stream()
            .filter(order -> orderId == null || order.getId().toString().contains(orderId))
            .filter(order -> tableNumber == null || order.getTableNumber().contains(tableNumber))
            .filter(order -> status == null || order.getStatus().equals(status))
            .collect(Collectors.toList());

        // 分页
        int start = (page - 1) * size;
        int end = Math.min(start + size, filteredOrders.size());
        List<Order> pageData = start < filteredOrders.size() ?
            filteredOrders.subList(start, end) : new ArrayList<>();

        return PageResult.build(pageData, (long) filteredOrders.size(), (long) page, (long) size);
    }

    /**
     * 获取模拟订单数据
     */
    private List<Order> getMockOrders() {
        List<Order> orders = new ArrayList<>();

        Order order1 = new Order();
        order1.setId(1001);
        order1.setTableNumber("8");
        order1.setUserId(1);
        order1.setUserName("admin");
        order1.setTotalPrice(new BigDecimal("156.50"));
        order1.setStatus("PENDING");
        order1.setCreatedAt(LocalDateTime.now().minusHours(2));
        order1.setUpdatedAt(LocalDateTime.now().minusHours(2));

        List<OrderItem> items1 = new ArrayList<>();
        OrderItem item1_1 = new OrderItem();
        item1_1.setDishName("宫保鸡丁");
        item1_1.setPrice(new BigDecimal("38.00"));
        item1_1.setQuantity(2);
        item1_1.setNotes("少辣");
        items1.add(item1_1);

        OrderItem item1_2 = new OrderItem();
        item1_2.setDishName("蛋炒饭");
        item1_2.setPrice(new BigDecimal("25.00"));
        item1_2.setQuantity(2);
        item1_2.setNotes("");
        items1.add(item1_2);

        OrderItem item1_3 = new OrderItem();
        item1_3.setDishName("可乐");
        item1_3.setPrice(new BigDecimal("10.00"));
        item1_3.setQuantity(3);
        item1_3.setNotes("加冰");
        items1.add(item1_3);

        order1.setItems(items1);
        orders.add(order1);

        Order order2 = new Order();
        order2.setId(1002);
        order2.setTableNumber("3");
        order2.setUserId(2);
        order2.setUserName("staff1");
        order2.setTotalPrice(new BigDecimal("89.00"));
        order2.setStatus("PREPARING");
        order2.setCreatedAt(LocalDateTime.now().minusHours(3));
        order2.setUpdatedAt(LocalDateTime.now().minusHours(1));

        List<OrderItem> items2 = new ArrayList<>();
        OrderItem item2_1 = new OrderItem();
        item2_1.setDishName("凉拌黄瓜");
        item2_1.setPrice(new BigDecimal("18.00"));
        item2_1.setQuantity(1);
        item2_1.setNotes("");
        items2.add(item2_1);

        OrderItem item2_2 = new OrderItem();
        item2_2.setDishName("红烧肉");
        item2_2.setPrice(new BigDecimal("45.00"));
        item2_2.setQuantity(1);
        item2_2.setNotes("");
        items2.add(item2_2);

        OrderItem item2_3 = new OrderItem();
        item2_3.setDishName("橙汁");
        item2_3.setPrice(new BigDecimal("12.00"));
        item2_3.setQuantity(2);
        item2_3.setNotes("");
        items2.add(item2_3);

        order2.setItems(items2);
        orders.add(order2);

        Order order3 = new Order();
        order3.setId(1003);
        order3.setTableNumber("12");
        order3.setUserId(3);
        order3.setUserName("staff2");
        order3.setTotalPrice(new BigDecimal("203.10"));
        order3.setStatus("COMPLETED");
        order3.setCreatedAt(LocalDateTime.now().minusHours(5));
        order3.setUpdatedAt(LocalDateTime.now().minusHours(1));
        order3.setItems(new ArrayList<>());
        orders.add(order3);

        Order order4 = new Order();
        order4.setId(1004);
        order4.setTableNumber("5");
        order4.setUserId(1);
        order4.setUserName("admin");
        order4.setTotalPrice(new BigDecimal("45.00"));
        order4.setStatus("CANCELLED");
        order4.setCreatedAt(LocalDateTime.now().minusHours(6));
        order4.setUpdatedAt(LocalDateTime.now().minusHours(4));
        order4.setItems(new ArrayList<>());
        orders.add(order4);

        Order order5 = new Order();
        order5.setId(1005);
        order5.setTableNumber("15");
        order5.setUserId(2);
        order5.setUserName("staff1");
        order5.setTotalPrice(new BigDecimal("78.00"));
        order5.setStatus("PENDING");
        order5.setCreatedAt(LocalDateTime.now().minusMinutes(30));
        order5.setUpdatedAt(LocalDateTime.now().minusMinutes(30));
        order5.setItems(new ArrayList<>());
        orders.add(order5);

        return orders;
    }

    /**
     * 获取模拟今日统计数据
     */
    private Map<String, Object> getMockTodayStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("todayOrders", 15);
        stats.put("pendingOrders", 3);
        stats.put("todaySales", new BigDecimal("1280.50"));
        stats.put("totalDishes", 28);
        return stats;
    }
}
