package com.hotel.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hotel.entity.OrderItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订单明细Mapper接口
 * 
 * <AUTHOR> System
 * @since 2025-07-15
 */
@Mapper
public interface OrderItemMapper extends BaseMapper<OrderItem> {

    /**
     * 根据订单ID查询订单明细（带菜品信息）
     */
    @Select("SELECT oi.*, d.name as dish_name, d.image_url as dish_image " +
            "FROM order_item oi " +
            "LEFT JOIN dish d ON oi.dish_id = d.id " +
            "WHERE oi.order_id = #{orderId} " +
            "ORDER BY oi.id")
    List<OrderItem> selectByOrderIdWithDish(@Param("orderId") Integer orderId);

    /**
     * 批量插入订单明细
     */
    int insertBatch(@Param("items") List<OrderItem> items);

    /**
     * 根据订单ID删除订单明细
     */
    int deleteByOrderId(@Param("orderId") Integer orderId);
}
