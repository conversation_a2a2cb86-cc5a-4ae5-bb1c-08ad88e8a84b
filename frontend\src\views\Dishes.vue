<template>
  <Layout>
    <div class="dishes">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>
          <el-icon><Food /></el-icon>
          菜品管理
        </h2>
        <el-button type="primary" @click="showAddDialog">
          <el-icon><Plus /></el-icon>
          添加菜品
        </el-button>
      </div>
      
      <!-- 搜索筛选 -->
      <el-card class="filter-card" shadow="never">
        <el-form :model="searchForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="菜品名称">
                <el-input
                  v-model="searchForm.name"
                  placeholder="请输入菜品名称"
                  clearable
                  @clear="handleSearch"
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="分类">
                <el-select v-model="searchForm.category" placeholder="请选择分类" clearable style="width: 100%">
                  <el-option label="开胃菜" value="APPETIZER" />
                  <el-option label="主菜" value="MAIN" />
                  <el-option label="甜点" value="DESSERT" />
                  <el-option label="饮品" value="DRINK" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 100%">
                  <el-option label="上架" :value="1" />
                  <el-option label="下架" :value="0" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" @click="handleSearch" size="default">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetSearch" size="default" style="margin-left: 10px;">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      
      <!-- 菜品列表 -->
      <div class="dishes-grid" v-loading="loading">
        <el-card
          v-for="dish in dishes"
          :key="dish.id"
          class="dish-card"
          shadow="hover"
        >
          <div class="dish-image">
            <img
              :src="dish.imageUrl || '/default-dish.svg'"
              :alt="dish.name"
              @error="handleImageError"
            />
            <div class="dish-status">
              <el-tag :type="dish.status === 1 ? 'success' : 'danger'">
                {{ dish.status === 1 ? '上架中' : '已下架' }}
              </el-tag>
            </div>
          </div>
          
          <div class="dish-info">
            <h3>{{ dish.name }}</h3>
            <p class="description">{{ dish.description }}</p>
            <div class="category">
              <el-tag :type="getCategoryType(dish.category)">
                {{ getCategoryText(dish.category) }}
              </el-tag>
            </div>
          </div>
          
          <div class="dish-footer">
            <div class="price">¥{{ dish.price }}</div>
          </div>

          <div class="dish-actions">
            <el-button type="primary" size="small" @click="editDish(dish)" style="margin-right: 8px;">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteDish(dish)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </div>
        </el-card>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[12, 24, 48]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    
    <!-- 添加/编辑菜品对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑菜品' : '添加菜品'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="dishFormRef"
        :model="dishForm"
        :rules="dishRules"
        label-width="80px"
      >
        <el-form-item label="菜品名称" prop="name">
          <el-input v-model="dishForm.name" placeholder="请输入菜品名称" />
        </el-form-item>
        
        <el-form-item label="菜品描述" prop="description">
          <el-input
            v-model="dishForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入菜品描述"
          />
        </el-form-item>
        
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="dishForm.price"
            :min="0"
            :precision="2"
            placeholder="请输入价格"
            style="width: 100%"
          />
        </el-form-item>
        
        <el-form-item label="分类" prop="category">
          <el-select v-model="dishForm.category" placeholder="请选择分类" style="width: 100%">
            <el-option label="开胃菜" value="APPETIZER" />
            <el-option label="主菜" value="MAIN" />
            <el-option label="甜点" value="DESSERT" />
            <el-option label="饮品" value="DRINK" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="dishForm.status">
            <el-radio :label="1">上架</el-radio>
            <el-radio :label="0">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="菜品图片">
          <el-upload
            class="image-uploader"
            :action="uploadUrl"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="dishForm.imageUrl" :src="dishForm.imageUrl" class="image" />
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">
          {{ isEdit ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Food, Plus, Search, Refresh, Edit, Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const authStore = useAuthStore()

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitLoading = ref(false)
const dishFormRef = ref()

const dishes = ref([])
const searchForm = reactive({
  name: '',
  category: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 12,
  total: 0
})

const dishForm = reactive({
  id: null,
  name: '',
  description: '',
  price: 0,
  category: '',
  status: 1,
  imageUrl: ''
})

const dishRules = {
  name: [
    { required: true, message: '请输入菜品名称', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入菜品描述', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

const uploadUrl = '/api/dishes/upload'
const uploadHeaders = {
  Authorization: `Bearer ${authStore.token}`
}

const loadDishes = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get('/api/dishes', { params })
    const { records, total } = response.data.data

    dishes.value = records
    pagination.total = total
  } catch (error) {
    console.error('加载菜品列表失败:', error)
    // 使用模拟数据
    dishes.value = [
      {
        id: 1,
        name: '凉拌黄瓜',
        description: '清爽开胃，夏日必备的凉菜',
        price: 18,
        category: 'APPETIZER',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 2,
        name: '宫保鸡丁',
        description: '经典川菜，香辣可口，下饭神器',
        price: 38,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 3,
        name: '蛋炒饭',
        description: '简单美味，老少皆宜的经典主食',
        price: 25,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 4,
        name: '清蒸鲈鱼',
        description: '鲜美嫩滑，营养丰富的健康菜品',
        price: 68.1,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 5,
        name: '可乐',
        description: '冰爽可乐，解腻必备饮品',
        price: 10,
        category: 'DRINK',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 6,
        name: '红烧肉',
        description: '肥而不腻，入口即化的经典菜品',
        price: 45,
        category: 'MAIN',
        status: 1,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 7,
        name: '提拉米苏',
        description: '意式经典甜品，浓郁香甜',
        price: 35,
        category: 'DESSERT',
        status: 0,
        imageUrl: '/default-dish.svg'
      },
      {
        id: 8,
        name: '酸辣土豆丝',
        description: '酸辣开胃，爽脆可口',
        price: 15,
        category: 'APPETIZER',
        status: 1,
        imageUrl: '/default-dish.svg'
      }
    ]
    pagination.total = dishes.value.length
    ElMessage.warning('使用模拟菜品数据')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadDishes()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    name: '',
    category: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadDishes()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadDishes()
}

const showAddDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const editDish = (dish) => {
  isEdit.value = true
  Object.assign(dishForm, dish)
  dialogVisible.value = true
}

const deleteDish = async (dish) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除菜品"${dish.name}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/api/dishes/${dish.id}`)
    ElMessage.success('删除成功')
    loadDishes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const resetForm = () => {
  Object.assign(dishForm, {
    id: null,
    name: '',
    description: '',
    price: 0,
    category: '',
    status: 1,
    imageUrl: ''
  })
  dishFormRef.value?.resetFields()
}

const submitForm = async () => {
  if (!dishFormRef.value) return

  await dishFormRef.value.validate(async (valid) => {
    if (valid) {
      submitLoading.value = true
      try {
        if (isEdit.value) {
          await api.put(`/api/dishes/${dishForm.id}`, dishForm)
          ElMessage.success('更新成功')
        } else {
          await api.post('/api/dishes', dishForm)
          ElMessage.success('添加成功')
        }

        dialogVisible.value = false
        loadDishes()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新失败' : '添加失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const handleImageSuccess = (response) => {
  if (response.code === 200) {
    dishForm.imageUrl = response.data.url
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error('图片上传失败')
  }
}

const beforeImageUpload = (file) => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('上传图片只能是 JPG/PNG 格式!')
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
  }
  return isJPG && isLt2M
}

const getCategoryType = (category) => {
  const typeMap = {
    'APPETIZER': 'danger',
    'MAIN': 'success',
    'DESSERT': 'warning',
    'DRINK': 'info'
  }
  return typeMap[category] || 'info'
}

const handleImageError = (event) => {
  // 如果图片加载失败，使用默认占位图
  event.target.src = `https://via.placeholder.com/300x200/f8f9fa/6c757d?text=${encodeURIComponent('暂无图片')}`
}

const getCategoryText = (category) => {
  const textMap = {
    'APPETIZER': '开胃菜',
    'MAIN': '主菜',
    'DESSERT': '甜点',
    'DRINK': '饮品'
  }
  return textMap[category] || category
}

onMounted(() => {
  loadDishes()
})
</script>

<style scoped>
.dishes {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3498db;
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.dishes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.dish-card {
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.dish-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.dish-image {
  position: relative;
  height: 180px;
  overflow: hidden;
}

.dish-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.dish-status {
  position: absolute;
  top: 10px;
  right: 10px;
}

.dish-info {
  padding: 15px;
}

.dish-info h3 {
  margin: 0 0 8px 0;
  color: #343a40;
  font-size: 1.1rem;
}

.description {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.category {
  margin-bottom: 10px;
}

.dish-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 15px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.price {
  font-size: 1.3rem;
  font-weight: bold;
  color: #3498db;
}

.dish-actions {
  padding: 15px;
  background: white;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: center;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
}

.image-uploader:hover {
  border-color: #409eff;
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

@media (max-width: 768px) {
  .dishes-grid {
    grid-template-columns: 1fr;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .actions {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
