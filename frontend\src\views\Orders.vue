<template>
  <Layout>
    <div class="orders">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>
          <el-icon><Document /></el-icon>
          订单管理
        </h2>
        <el-button type="primary" @click="showCreateDialog">
          <el-icon><Plus /></el-icon>
          创建订单
        </el-button>
      </div>
      
      <!-- 搜索筛选 -->
      <el-card class="filter-card" shadow="never">
        <el-form :model="searchForm" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="订单号">
                <el-input
                  v-model="searchForm.orderId"
                  placeholder="请输入订单号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="桌号">
                <el-input
                  v-model="searchForm.tableNumber"
                  placeholder="请输入桌号"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="状态">
                <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 100%">
                  <el-option label="待处理" value="PENDING" />
                  <el-option label="准备中" value="PREPARING" />
                  <el-option label="已完成" value="COMPLETED" />
                  <el-option label="已取消" value="CANCELLED" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" ">
                <el-button type="primary" @click="handleSearch" size="default">
                  <el-icon><Search /></el-icon>
                  搜索
                </el-button>
                <el-button @click="resetSearch" size="default" style="margin-left: 10px;">
                  <el-icon><Refresh /></el-icon>
                  重置
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      
      <!-- 订单列表 -->
      <el-card shadow="hover">
        <el-table :data="orders" v-loading="loading" stripe>
          <el-table-column prop="id" label="订单号" width="120">
            <template #default="{ row }">
              #{{ row.id }}
            </template>
          </el-table-column>
          <el-table-column prop="tableNumber" label="桌号" width="100" />
          <el-table-column prop="userName" label="服务员" width="120" />
          <el-table-column prop="totalPrice" label="金额" width="120">
            <template #default="{ row }">
              ¥{{ row.totalPrice }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="180" />
          <el-table-column label="操作" width="280">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="viewOrder(row)">
                  <el-icon><View /></el-icon>
                  查看
                </el-button>
                <el-button
                  v-if="row.status === 'PENDING'"
                  type="warning"
                  size="small"
                  @click="updateStatus(row, 'PREPARING')"
                >
                  <el-icon><Timer /></el-icon>
                  开始制作
                </el-button>
                <el-button
                  v-if="row.status === 'PREPARING'"
                  type="success"
                  size="small"
                  @click="updateStatus(row, 'COMPLETED')"
                >
                  <el-icon><Check /></el-icon>
                  完成
                </el-button>
                <el-button
                  v-if="['PENDING', 'PREPARING'].includes(row.status)"
                  type="info"
                  size="small"
                  @click="updateStatus(row, 'CANCELLED')"
                >
                  <el-icon><Close /></el-icon>
                  取消
                </el-button>
                <el-button
                  type="danger"
                  size="small"
                  @click="deleteOrder(row)"
                >
                  <el-icon><Delete /></el-icon>
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :page-sizes="[10, 20, 50]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 创建订单对话框 -->
    <el-dialog
      v-model="createDialogVisible"
      title="创建订单"
      width="800px"
      @close="resetCreateForm"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="80px"
      >
        <el-form-item label="桌号" prop="tableNumber">
          <el-input v-model="createForm.tableNumber" placeholder="请输入桌号" />
        </el-form-item>
        
        <el-form-item label="选择菜品">
          <div class="dish-selection">
            <el-row :gutter="20">
              <el-col
                v-for="dish in availableDishes"
                :key="dish.id"
                :span="8"
                style="margin-bottom: 15px"
              >
                <el-card class="dish-item" shadow="hover" @click="addDishToOrder(dish)">
                  <div class="dish-content">
                    <img :src="dish.imageUrl || '/default-dish.svg'" :alt="dish.name" />
                    <div class="dish-details">
                      <h4>{{ dish.name }}</h4>
                      <p>¥{{ dish.price }}</p>
                    </div>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-form-item>
        
        <el-form-item label="已选菜品">
          <el-table :data="createForm.items" style="width: 100%">
            <el-table-column prop="dishName" label="菜品名称" />
            <el-table-column prop="price" label="单价">
              <template #default="{ row }">
                ¥{{ row.price }}
              </template>
            </el-table-column>
            <el-table-column prop="quantity" label="数量" width="120">
              <template #default="{ row, $index }">
                <el-input-number
                  v-model="row.quantity"
                  :min="1"
                  size="small"
                  @change="updateOrderTotal"
                />
              </template>
            </el-table-column>
            <el-table-column label="小计" width="100">
              <template #default="{ row }">
                ¥{{ (row.price * row.quantity).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row, $index }">
                <el-button
                  type="text"
                  size="small"
                  @click="removeDishFromOrder($index)"
                  style="color: #f56c6c"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          
          <div class="order-total">
            <strong>总计: ¥{{ createForm.totalPrice.toFixed(2) }}</strong>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="createDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitCreateForm">
          创建订单
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 查看订单详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="订单详情"
      width="600px"
    >
      <div v-if="selectedOrder">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="订单号">#{{ selectedOrder.id }}</el-descriptions-item>
          <el-descriptions-item label="桌号">{{ selectedOrder.tableNumber }}</el-descriptions-item>
          <el-descriptions-item label="服务员">{{ selectedOrder.userName }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="总金额">¥{{ selectedOrder.totalPrice }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ selectedOrder.createdAt }}</el-descriptions-item>
        </el-descriptions>
        
        <h3 style="margin: 20px 0 10px 0">订单明细</h3>
        <el-table :data="selectedOrder.items" style="width: 100%">
          <el-table-column prop="dishName" label="菜品名称" />
          <el-table-column prop="price" label="单价">
            <template #default="{ row }">
              ¥{{ row.price }}
            </template>
          </el-table-column>
          <el-table-column prop="quantity" label="数量" />
          <el-table-column label="小计">
            <template #default="{ row }">
              ¥{{ (row.price * row.quantity).toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="notes" label="备注" />
        </el-table>
      </div>
    </el-dialog>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document, Plus, Search, Refresh, View, Timer, Check, Close, Delete
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import api from '@/utils/api'

const loading = ref(false)
const createDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const submitLoading = ref(false)
const createFormRef = ref()

const orders = ref([])
const availableDishes = ref([])
const selectedOrder = ref(null)

const searchForm = reactive({
  orderId: '',
  tableNumber: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const createForm = reactive({
  tableNumber: '',
  items: [],
  totalPrice: 0
})

const createRules = {
  tableNumber: [
    { required: true, message: '请输入桌号', trigger: 'blur' }
  ]
}

const loadOrders = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchForm
    }

    const response = await api.get('/api/orders', { params })
    const { records, total } = response.data.data

    orders.value = records
    pagination.total = total
  } catch (error) {
    console.error('加载订单列表失败:', error)
    // 使用模拟数据
    orders.value = [
      {
        id: 1001,
        tableNumber: '8',
        userName: 'admin',
        totalPrice: 156.50,
        status: 'PENDING',
        createdAt: '2024-01-15 12:30:00',
        items: [
          { dishName: '宫保鸡丁', price: 38, quantity: 2, notes: '少辣' },
          { dishName: '蛋炒饭', price: 25, quantity: 2, notes: '' },
          { dishName: '可乐', price: 10, quantity: 3, notes: '加冰' }
        ]
      },
      {
        id: 1002,
        tableNumber: '3',
        userName: 'staff1',
        totalPrice: 89.00,
        status: 'PREPARING',
        createdAt: '2024-01-15 12:15:00',
        items: [
          { dishName: '凉拌黄瓜', price: 18, quantity: 1, notes: '' },
          { dishName: '红烧肉', price: 45, quantity: 1, notes: '' },
          { dishName: '橙汁', price: 12, quantity: 2, notes: '' }
        ]
      },
      {
        id: 1003,
        tableNumber: '12',
        userName: 'staff2',
        totalPrice: 203.10,
        status: 'COMPLETED',
        createdAt: '2024-01-15 11:45:00',
        items: [
          { dishName: '清蒸鲈鱼', price: 68.1, quantity: 1, notes: '清淡' },
          { dishName: '宫保鸡丁', price: 38, quantity: 2, notes: '' },
          { dishName: '蛋炒饭', price: 25, quantity: 1, notes: '' },
          { dishName: '可乐', price: 10, quantity: 4, notes: '' }
        ]
      },
      {
        id: 1004,
        tableNumber: '5',
        userName: 'admin',
        totalPrice: 45.00,
        status: 'CANCELLED',
        createdAt: '2024-01-15 11:20:00',
        items: [
          { dishName: '红烧肉', price: 45, quantity: 1, notes: '客人临时取消' }
        ]
      },
      {
        id: 1005,
        tableNumber: '15',
        userName: 'staff1',
        totalPrice: 78.00,
        status: 'PENDING',
        createdAt: '2024-01-15 12:45:00',
        items: [
          { dishName: '凉拌黄瓜', price: 18, quantity: 2, notes: '' },
          { dishName: '酸辣土豆丝', price: 15, quantity: 2, notes: '多醋' },
          { dishName: '橙汁', price: 12, quantity: 2, notes: '' }
        ]
      }
    ]
    pagination.total = orders.value.length
    ElMessage.warning('使用模拟订单数据')
  } finally {
    loading.value = false
  }
}

const loadAvailableDishes = async () => {
  try {
    const response = await api.get('/api/dishes/available')
    availableDishes.value = response.data.data
  } catch (error) {
    ElMessage.error('加载可用菜品失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadOrders()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    orderId: '',
    tableNumber: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadOrders()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadOrders()
}

const showCreateDialog = () => {
  createDialogVisible.value = true
  loadAvailableDishes()
}

const viewOrder = async (order) => {
  try {
    const response = await api.get(`/api/orders/${order.id}`)
    selectedOrder.value = response.data.data
    detailDialogVisible.value = true
  } catch (error) {
    ElMessage.error('加载订单详情失败')
  }
}

const updateStatus = async (order, newStatus) => {
  try {
    await api.patch(`/api/orders/${order.id}/status`, { status: newStatus })
    ElMessage.success('状态更新成功')
    loadOrders()
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const deleteOrder = async (order) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除订单 #${order.id} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/api/orders/${order.id}`)
    ElMessage.success('删除成功')
    loadOrders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const addDishToOrder = (dish) => {
  const existingItem = createForm.items.find(item => item.dishId === dish.id)

  if (existingItem) {
    existingItem.quantity += 1
  } else {
    createForm.items.push({
      dishId: dish.id,
      dishName: dish.name,
      price: dish.price,
      quantity: 1,
      notes: ''
    })
  }

  updateOrderTotal()
}

const removeDishFromOrder = (index) => {
  createForm.items.splice(index, 1)
  updateOrderTotal()
}

const updateOrderTotal = () => {
  createForm.totalPrice = createForm.items.reduce(
    (total, item) => total + (item.price * item.quantity),
    0
  )
}

const resetCreateForm = () => {
  Object.assign(createForm, {
    tableNumber: '',
    items: [],
    totalPrice: 0
  })
  createFormRef.value?.resetFields()
}

const submitCreateForm = async () => {
  if (!createFormRef.value) return

  await createFormRef.value.validate(async (valid) => {
    if (valid) {
      if (createForm.items.length === 0) {
        ElMessage.warning('请至少选择一个菜品')
        return
      }

      submitLoading.value = true
      try {
        await api.post('/api/orders', createForm)
        ElMessage.success('订单创建成功')
        createDialogVisible.value = false
        loadOrders()
      } catch (error) {
        ElMessage.error('订单创建失败')
      } finally {
        submitLoading.value = false
      }
    }
  })
}

const getStatusType = (status) => {
  const statusMap = {
    'PENDING': 'warning',
    'PREPARING': 'info',
    'COMPLETED': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'PENDING': '待处理',
    'PREPARING': '准备中',
    'COMPLETED': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.orders {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #3498db;
  margin: 0;
}

.filter-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.action-buttons .el-button {
  margin: 0;
  padding: 5px 8px;
  font-size: 12px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.dish-selection {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
}

.dish-item {
  cursor: pointer;
  transition: transform 0.2s;
}

.dish-item:hover {
  transform: scale(1.02);
}

.dish-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dish-content img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

.dish-details h4 {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
}

.dish-details p {
  margin: 0;
  color: #3498db;
  font-weight: bold;
}

.order-total {
  text-align: right;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .dish-content {
    flex-direction: column;
    text-align: center;
  }
}
</style>
