<template>
  <Layout>
    <div class="profile-container">
      <div class="page-header">
        <h2>
          <el-icon><User /></el-icon>
          个人信息
        </h2>
      </div>

      <div class="profile-content">
        <!-- 基本信息卡片 -->
        <el-card class="info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><UserFilled /></el-icon>
                基本信息
              </h3>
              <el-button type="primary" @click="editMode = !editMode">
                <el-icon><Edit /></el-icon>
                {{ editMode ? '取消编辑' : '编辑信息' }}
              </el-button>
            </div>
          </template>

          <div class="profile-info" v-loading="loading">
            <div class="avatar-section">
              <el-avatar :size="120" :src="userInfo.avatar" class="user-avatar">
                <el-icon><UserFilled /></el-icon>
              </el-avatar>
              <div v-if="editMode" class="avatar-upload">
                <el-upload
                  :action="uploadUrl"
                  :headers="uploadHeaders"
                  :show-file-list="false"
                  :on-success="handleAvatarSuccess"
                  :before-upload="beforeAvatarUpload"
                  accept="image/*"
                >
                  <el-button size="small" type="text">
                    <el-icon><Camera /></el-icon>
                    更换头像
                  </el-button>
                </el-upload>
              </div>
            </div>

            <div class="info-form">
              <el-form
                ref="profileFormRef"
                :model="userForm"
                :rules="formRules"
                label-width="100px"
                :disabled="!editMode"
              >
                <el-form-item label="用户名" prop="username">
                  <el-input v-model="userForm.username" disabled />
                </el-form-item>

                <el-form-item label="姓名" prop="name">
                  <el-input v-model="userForm.name" placeholder="请输入姓名" />
                </el-form-item>

                <el-form-item label="角色">
                  <el-tag :type="getRoleType(userForm.role)">
                    {{ getRoleText(userForm.role) }}
                  </el-tag>
                </el-form-item>

                <el-form-item label="状态">
                  <el-tag :type="userForm.status === 1 ? 'success' : 'danger'">
                    {{ userForm.status === 1 ? '正常' : '禁用' }}
                  </el-tag>
                </el-form-item>

                <el-form-item label="注册时间">
                  <span class="info-text">{{ formatTime(userInfo.createdAt) }}</span>
                </el-form-item>

                <el-form-item label="最后登录">
                  <span class="info-text">{{ formatTime(userInfo.lastLoginAt) }}</span>
                </el-form-item>

                <el-form-item v-if="editMode">
                  <el-button type="primary" @click="saveProfile" :loading="saveLoading">
                    <el-icon><Check /></el-icon>
                    保存修改
                  </el-button>
                  <el-button @click="resetForm">
                    <el-icon><RefreshLeft /></el-icon>
                    重置
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-card>

        <!-- 密码修改卡片 -->
        <el-card class="password-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Lock /></el-icon>
                修改密码
              </h3>
            </div>
          </template>

          <el-form
            ref="passwordFormRef"
            :model="passwordForm"
            :rules="passwordRules"
            label-width="120px"
            class="password-form"
          >
            <el-form-item label="当前密码" prop="currentPassword">
              <el-input
                v-model="passwordForm.currentPassword"
                type="password"
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="新密码" prop="newPassword">
              <el-input
                v-model="passwordForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input
                v-model="passwordForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="changePassword" :loading="passwordLoading">
                <el-icon><Key /></el-icon>
                修改密码
              </el-button>
              <el-button @click="resetPasswordForm">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 操作记录卡片 -->
        <el-card class="activity-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <h3>
                <el-icon><Clock /></el-icon>
                最近活动
              </h3>
            </div>
          </template>

          <div class="activity-list">
            <div
              v-for="activity in recentActivities"
              :key="activity.id"
              class="activity-item"
            >
              <div class="activity-icon">
                <el-icon v-if="activity.type === 'login'" class="login-icon">
                  <User />
                </el-icon>
                <el-icon v-else-if="activity.type === 'order'" class="order-icon">
                  <Document />
                </el-icon>
                <el-icon v-else class="default-icon">
                  <Operation />
                </el-icon>
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ formatTime(activity.time) }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  User, UserFilled, Edit, Check, RefreshLeft, Lock, Key, Clock, Camera, Document, Operation
} from '@element-plus/icons-vue'
import Layout from '@/components/Layout.vue'
import { useAuthStore } from '@/stores/auth'
import api from '@/utils/api'

const authStore = useAuthStore()
const loading = ref(false)
const editMode = ref(false)
const saveLoading = ref(false)
const passwordLoading = ref(false)
const profileFormRef = ref()
const passwordFormRef = ref()

const userInfo = ref({})
const userForm = reactive({
  username: '',
  name: '',
  role: '',
  status: 1,
  avatar: ''
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const recentActivities = ref([])

// 上传配置
const uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/api/upload/avatar`
const uploadHeaders = {
  'Authorization': `Bearer ${authStore.token}`
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '姓名长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

// 加载用户信息
const loadUserInfo = async () => {
  loading.value = true
  try {
    const response = await api.get('/api/auth/me')
    userInfo.value = response.data.data
    
    // 填充表单
    Object.assign(userForm, {
      username: userInfo.value.username,
      name: userInfo.value.name,
      role: userInfo.value.role,
      status: userInfo.value.status,
      avatar: userInfo.value.avatar
    })
  } catch (error) {
    console.error('加载用户信息失败:', error)
    // 使用当前用户信息
    const currentUser = authStore.user
    userInfo.value = {
      ...currentUser,
      createdAt: '2024-01-01T00:00:00',
      lastLoginAt: new Date().toISOString()
    }
    Object.assign(userForm, currentUser)
    ElMessage.warning('使用缓存用户信息')
  } finally {
    loading.value = false
  }
}

// 保存个人信息
const saveProfile = async () => {
  if (!profileFormRef.value) return
  
  await profileFormRef.value.validate(async (valid) => {
    if (valid) {
      saveLoading.value = true
      try {
        await api.put('/api/auth/profile', {
          name: userForm.name,
          avatar: userForm.avatar
        })
        
        // 更新本地用户信息
        authStore.updateUser({
          ...authStore.user,
          name: userForm.name,
          avatar: userForm.avatar
        })
        
        editMode.value = false
        ElMessage.success('个人信息更新成功')
      } catch (error) {
        console.error('更新个人信息失败:', error)
        ElMessage.error('更新失败')
      } finally {
        saveLoading.value = false
      }
    }
  })
}

// 修改密码
const changePassword = async () => {
  if (!passwordFormRef.value) return
  
  await passwordFormRef.value.validate(async (valid) => {
    if (valid) {
      passwordLoading.value = true
      try {
        await api.post('/api/auth/change-password', {
          currentPassword: passwordForm.currentPassword,
          newPassword: passwordForm.newPassword
        })
        
        resetPasswordForm()
        ElMessage.success('密码修改成功')
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error(error.response?.data?.message || '密码修改失败')
      } finally {
        passwordLoading.value = false
      }
    }
  })
}

// 头像上传成功
const handleAvatarSuccess = (response) => {
  if (response.code === 200) {
    userForm.avatar = response.data.url
    ElMessage.success('头像上传成功')
  } else {
    ElMessage.error('头像上传失败')
  }
}

// 头像上传前验证
const beforeAvatarUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
  }
  return isImage && isLt2M
}

// 重置表单
const resetForm = () => {
  Object.assign(userForm, {
    username: userInfo.value.username,
    name: userInfo.value.name,
    role: userInfo.value.role,
    status: userInfo.value.status,
    avatar: userInfo.value.avatar
  })
}

const resetPasswordForm = () => {
  Object.assign(passwordForm, {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  passwordFormRef.value?.clearValidate()
}

// 获取角色类型
const getRoleType = (role) => {
  const typeMap = {
    'ADMIN': 'danger',
    'MANAGER': 'warning',
    'STAFF': 'success'
  }
  return typeMap[role] || 'info'
}

// 获取角色文本
const getRoleText = (role) => {
  const textMap = {
    'ADMIN': '管理员',
    'MANAGER': '经理',
    'STAFF': '员工'
  }
  return textMap[role] || role
}

// 格式化时间
const formatTime = (timeStr) => {
  if (!timeStr) return '暂无'
  return new Date(timeStr).toLocaleString()
}

// 加载最近活动
const loadRecentActivities = () => {
  recentActivities.value = [
    {
      id: 1,
      type: 'login',
      title: '登录系统',
      time: new Date().toISOString()
    },
    {
      id: 2,
      type: 'order',
      title: '处理订单 #2024001',
      time: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: 3,
      type: 'edit',
      title: '修改菜品信息',
      time: new Date(Date.now() - 7200000).toISOString()
    }
  ]
}

onMounted(() => {
  loadUserInfo()
  loadRecentActivities()
})
</script>

<style scoped>
.profile-container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.page-header h2 {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  color: #3498db;
  font-size: 1.5rem;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #3498db;
}

.profile-info {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}

.user-avatar {
  border: 3px solid #e9ecef;
}

.avatar-upload {
  text-align: center;
}

.info-form {
  flex: 1;
}

.info-text {
  color: #6c757d;
}

.password-form {
  max-width: 500px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.login-icon {
  background: #e3f2fd;
  color: #1976d2;
}

.order-icon {
  background: #f3e5f5;
  color: #7b1fa2;
}

.default-icon {
  background: #e8f5e8;
  color: #388e3c;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #343a40;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 0.9rem;
  color: #6c757d;
}

@media (max-width: 768px) {
  .profile-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .info-form {
    width: 100%;
  }

  .password-form {
    max-width: none;
  }
}
</style>
