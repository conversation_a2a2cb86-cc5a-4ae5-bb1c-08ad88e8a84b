package com.hotel;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 酒店点单系统启动类
 * 
 * <AUTHOR> System
 * @since 2025-07-15
 */
@SpringBootApplication
@MapperScan("com.hotel.mapper")
@EnableTransactionManagement
public class HotelOrderSystemApplication {

    public static void main(String[] args) {
        SpringApplication.run(HotelOrderSystemApplication.class, args);
        System.out.println("=================================");
        System.out.println("酒店点单系统启动成功！");
        System.out.println("访问地址：http://localhost:8080");
        System.out.println("=================================");
    }
}
